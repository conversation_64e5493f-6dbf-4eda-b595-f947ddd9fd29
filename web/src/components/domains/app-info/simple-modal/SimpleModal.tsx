import * as React from 'react';
import {
  <PERSON><PERSON>, Header, Input,
} from '@fluentui/react-northstar';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import BookmarkStar from '../../../commons/atoms/bookmark-star/BookmarkStar';

// CSS
import './SimpleModal.scss';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  isBookmarked: boolean;
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  title?: string;
  onClose?: () => void;
  chatItems?: IChatItem[];
  onChatItemBookmarkToggle?: (id: string, isBookmarked: boolean) => void;
}

/**
 * SimpleModal
 * @param props
 */
const SimpleModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    title = 'Teams設定',
    onClose,
    chatItems = [
      {
        id: '1', name: '<PERSON> Asenkita', type: 'チャット', isBookmarked: true,
      },
      {
        id: '2', name: 'miTane_DevOps チーム名', type: 'チャネル', isBookmarked: false,
      },
      {
        id: '3', name: '本店 営業', type: 'チャット', isBookmarked: false,
      },
      {
        id: '4', name: 'Jan Asenkita', type: 'チャット', isBookmarked: false,
      },
    ],
    onChatItemBookmarkToggle,
  } = props;

  // チャットID入力の状態管理
  const [chatId, setChatId] = React.useState('');
  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    return mergedClassName(isOpen, step1);
  }, [className, open]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // チャットID入力の変更ハンドラー
  const handleChatIdChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setChatId(data?.value ?? '');
    },
    [],
  );

  // ブックマーク切り替えハンドラー
  const handleBookmarkToggle = React.useCallback((itemId: string, isBookmarked: boolean) => {
    if (onChatItemBookmarkToggle) {
      onChatItemBookmarkToggle(itemId, isBookmarked);
    }
  }, [onChatItemBookmarkToggle]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={title} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>★をつけると検索対象になります。</p>

            {/* チャットID入力フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder="チャットIDを入力"
                value={chatId}
                onChange={handleChatIdChange}
                fluid
              />
            </div>

            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {chatItems.map((item) => (
                <div key={item.id} className="simple-modal-chat-item">
                  <div className="simple-modal-chat-item-content">
                    <span className="simple-modal-chat-item-label">
                      {item.type}
                      ：
                    </span>
                    <span className="simple-modal-chat-item-name">{item.name}</span>
                  </div>
                  <BookmarkStar
                    isBookmarked={item.isBookmarked}
                    onClick={(toBe) => handleBookmarkToggle(item.id, toBe)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

SimpleModal.defaultProps = {
  className: '',
  open: false,
  title: undefined,
  onClose: undefined,
  chatItems: [],
  onChatItemBookmarkToggle: undefined,
};

export default SimpleModal;
